package com.cloudpod.podsail.service.cloudpods.serversku.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * CloudPods ServerSku 创建请求参数
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Data
@Accessors(chain = true)
public class CloudPodsServerSkuCreateRequest {

    /**
     * 套餐规格名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * CPU核数
     */
    private Integer cpuCoreCount;

    /**
     * 内存大小(MB)
     */
    private Integer memorySizeMb;

    /**
     * 本地分类
     */
    private String localCategory;

    /**
     * 实例类型族
     */
    private String instanceTypeFamily;

    /**
     * 实例类型
     */
    private String instanceType;

    /**
     * 云提供商
     */
    private String provider;

    /**
     * 云区域ID
     */
    private String cloudregionId;

    /**
     * 可用区ID
     */
    private String zoneId;

    /**
     * 是否启用
     */
    private Boolean enabled = true;

    /**
     * 是否为公有云
     */
    private Boolean publicCloud = false;

    /**
     * 后付费状态
     */
    private String postpaidStatus;

    /**
     * 预付费状态
     */
    private String prepaidStatus;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 域ID
     */
    private String domainId;

    /**
     * 虚拟化类型
     */
    private String hypervisor;

    /**
     * 操作系统架构
     */
    private String osArch;

    /**
     * GPU数量
     */
    private Integer gpuCount;

    /**
     * GPU规格
     */
    private String gpuSpec;

    /**
     * 网络最大带宽
     */
    private Integer networkMaxBandwidth;

    /**
     * 网络最大连接数
     */
    private Integer networkMaxConnections;

    /**
     * 存储类型
     */
    private String storageType;

    /**
     * 数据盘类型
     */
    private String dataDiskTypes;

    /**
     * 数据盘最大数量
     */
    private Integer dataDiskMaxCount;

    /**
     * 数据盘最大容量
     */
    private Integer dataDiskMaxSizeGb;

    /**
     * 系统盘最小容量
     */
    private Integer sysDiskMinSizeGb;

    /**
     * 系统盘最大容量
     */
    private Integer sysDiskMaxSizeGb;

    /**
     * 系统盘类型
     */
    private String sysDiskType;

    /**
     * 是否支持热插拔
     */
    private Boolean hotplugCpuMem;

    /**
     * 标签
     */
    private Map<String, String> tags;

    /**
     * 元数据
     */
    private Map<String, Object> metadata;

    /**
     * 扩展属性
     */
    private Map<String, Object> extendedProperties;
}
