package com.cloudpod.podsail.service.cloudpods.serversku.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * CloudPods ServerSku 查询请求参数
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Data
@Accessors(chain = true)
public class CloudPodsServerSkuQueryRequest {

    /**
     * 分页限制
     */
    private Integer limit = 20;

    /**
     * 分页偏移量
     */
    private Integer offset = 0;

    /**
     * 排序字段
     */
    private String orderBy = "created_at";

    /**
     * 排序方式 (asc/desc)
     */
    private String order = "desc";

    /**
     * 是否返回详细信息
     */
    private Boolean details = false;

    /**
     * 搜索关键词
     */
    private String search;

    /**
     * 过滤条件
     */
    private String filter;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 是否可用
     */
    private Boolean usable;

    /**
     * 是否为公有云
     */
    private Boolean publicCloud;

    /**
     * CPU核数
     */
    private Integer cpuCoreCount;

    /**
     * 内存大小(MB)
     */
    private Integer memorySizeMb;

    /**
     * 云区域
     */
    private String cloudregion;

    /**
     * 云提供商
     */
    private String provider;

    /**
     * 作用域
     */
    private String scope;

    /**
     * 后付费状态
     */
    private String postpaidStatus;

    /**
     * 本地分类
     */
    private String localCategory;

    /**
     * 是否去重
     */
    private Boolean distinct;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 域ID
     */
    private String domainId;

    /**
     * 可用区
     */
    private String zone;

    /**
     * 虚拟化类型
     */
    private String hypervisor;

    /**
     * 套餐名称
     */
    private String name;

    /**
     * 套餐ID
     */
    private String id;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建时间范围开始
     */
    private String createdAtStart;

    /**
     * 创建时间范围结束
     */
    private String createdAtEnd;

    /**
     * 更新时间范围开始
     */
    private String updatedAtStart;

    /**
     * 更新时间范围结束
     */
    private String updatedAtEnd;
}
