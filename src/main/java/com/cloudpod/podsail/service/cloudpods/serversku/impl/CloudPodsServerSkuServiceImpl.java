package com.cloudpod.podsail.service.cloudpods.serversku.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpod.podsail.config.CloudPodClientConfig;
import com.cloudpod.podsail.service.cloudpods.serversku.dto.*;
import com.cloudpod.podsail.service.cloudpods.serversku.CloudPodsServerSkuService;
import com.yunionyun.mcp.mcclient.AuthAgent;
import com.yunionyun.mcp.mcclient.Session;
import com.yunionyun.mcp.mcclient.EndpointType;
import com.yunionyun.mcp.mcclient.managers.ListResult;
import com.yunionyun.mcp.mcclient.managers.impl.compute.SkuManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * CloudPods ServerSku 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Slf4j
@Service
public class CloudPodsServerSkuServiceImpl implements CloudPodsServerSkuService {
    
    @Autowired
    private AuthAgent authAgent;
    
    @Autowired
    private CloudPodClientConfig config;
    
    private Session getAdminSession() {
        return authAgent.getAdminSession(config.getDefaultRegion(), config.getDefaultZone(),
                EndpointType.ApigatewayURL);
    }
    
    @Override
    public CloudPodsServerSkuListResponse getServerSkuList(CloudPodsServerSkuQueryRequest queryRequest) {
        try {
            log.info("查询ServerSku列表: {}", queryRequest);
            
            Session adminSession = getAdminSession();
            SkuManager serverSkuManager = new SkuManager();
            
            // 构建查询参数
            JSONObject queryParams = buildServerSkuQueryParams(queryRequest);
            log.info("CloudPods ServerSku查询参数: {}", queryParams.toJSONString());
            
            ListResult listResult = serverSkuManager.List(adminSession, queryParams);
            log.info("CloudPods查询ServerSku列表响应: 总数={}", listResult.getTotal());
            
            return parseServerSkuListResponse(listResult);
            
        } catch (Exception e) {
            log.error("查询ServerSku列表失败", e);
            return new CloudPodsServerSkuListResponse()
                    .setSuccess(false)
                    .setErrorMessage("查询ServerSku列表失败: " + e.getMessage());
        }
    }
    
    @Override
    public CloudPodsServerSkuDetailResponse getServerSkuDetail(String skuId) {
        try {
            log.info("获取ServerSku详情: {}", skuId);
            
            Session adminSession = getAdminSession();
            SkuManager serverSkuManager = new SkuManager();
            
            JSONObject params = new JSONObject();
            params.put("details", true);
            
            JSONObject result = serverSkuManager.Get(adminSession, skuId, params);
            log.info("获取ServerSku详情响应: {}", result.toJSONString());
            
            return parseServerSkuDetailResponse(result);
            
        } catch (Exception e) {
            log.error("获取ServerSku详情失败: {}", skuId, e);
            return new CloudPodsServerSkuDetailResponse()
                    .setSuccess(false)
                    .setErrorMessage("获取ServerSku详情失败: " + e.getMessage());
        }
    }
    
    @Override
    public CloudPodsServerSkuCreateResponse createServerSku(CloudPodsServerSkuCreateRequest createRequest) {
        try {
            log.info("创建ServerSku请求: {}", createRequest);
            
            Session adminSession = getAdminSession();
            SkuManager serverSkuManager = new SkuManager();
            
            // 构建创建参数
            JSONObject createParams = buildCreateServerSkuParams(createRequest);
            log.info("CloudPods创建ServerSku参数: {}", createParams.toJSONString());
            
            // 调用CloudPods API创建ServerSku
            JSONObject result = serverSkuManager.Create(adminSession, createParams);
            log.info("CloudPods创建ServerSku响应: {}", result.toJSONString());
            
            return parseCreateServerSkuResponse(result);
            
        } catch (Exception e) {
            log.error("创建ServerSku失败", e);
            return CloudPodsServerSkuCreateResponse.failure("创建ServerSku失败: " + e.getMessage());
        }
    }
    
    @Override
    public CloudPodsServerSkuUpdateResponse updateServerSku(String skuId,
                                                            CloudPodsServerSkuUpdateRequest updateRequest) {
        try {
            log.info("更新ServerSku: {}, 请求: {}", skuId, updateRequest);
            
            Session adminSession = getAdminSession();
            SkuManager serverSkuManager = new SkuManager();
            
            // 构建更新参数
            JSONObject updateParams = buildUpdateServerSkuParams(updateRequest);
            log.info("CloudPods更新ServerSku参数: {}", updateParams.toJSONString());
            
            JSONObject result = serverSkuManager.Update(adminSession, skuId, updateParams);
            log.info("CloudPods更新ServerSku响应: {}", result.toJSONString());
            
            return parseUpdateServerSkuResponse(result);
            
        } catch (Exception e) {
            log.error("更新ServerSku失败: {}", skuId, e);
            return CloudPodsServerSkuUpdateResponse.failure("更新ServerSku失败: " + e.getMessage());
        }
    }
    
    @Override
    public CloudPodsServerSkuDeleteResponse deleteServerSku(String skuId) {
        try {
            log.info("删除ServerSku: {}", skuId);
            
            Session adminSession = getAdminSession();
            SkuManager serverSkuManager = new SkuManager();
            
            JSONObject result = serverSkuManager.Delete(adminSession, skuId);
            log.info("CloudPods删除ServerSku响应: {}", result.toJSONString());
            
            return parseDeleteServerSkuResponse(result, skuId);
            
        } catch (Exception e) {
            log.error("删除ServerSku失败: {}", skuId, e);
            return CloudPodsServerSkuDeleteResponse.failure("删除ServerSku失败: " + e.getMessage());
        }
    }
    
    @Override
    public CloudPodsServerSkuOperationResponse enableServerSku(String skuId) {
        try {
            log.info("启用ServerSku: {}", skuId);
            
            Session adminSession = getAdminSession();
            SkuManager serverSkuManager = new SkuManager();
            
            JSONObject params = new JSONObject();
            JSONObject result = serverSkuManager.PerformAction(adminSession, skuId, "enable", params);
            
            log.info("启用ServerSku响应: {}", result.toJSONString());
            return CloudPodsServerSkuOperationResponse.success("enable", skuId, result.toJSONString());
            
        } catch (Exception e) {
            log.error("启用ServerSku失败: {}", skuId, e);
            return CloudPodsServerSkuOperationResponse.failure("enable", "启用ServerSku失败: " + e.getMessage());
        }
    }
    
    @Override
    public CloudPodsServerSkuOperationResponse disableServerSku(String skuId) {
        try {
            log.info("禁用ServerSku: {}", skuId);
            
            Session adminSession = getAdminSession();
            SkuManager serverSkuManager = new SkuManager();
            
            JSONObject params = new JSONObject();
            JSONObject result = serverSkuManager.PerformAction(adminSession, skuId, "disable", params);
            
            log.info("禁用ServerSku响应: {}", result.toJSONString());
            return CloudPodsServerSkuOperationResponse.success("disable", skuId, result.toJSONString());
            
        } catch (Exception e) {
            log.error("禁用ServerSku失败: {}", skuId, e);
            return CloudPodsServerSkuOperationResponse.failure("disable", "禁用ServerSku失败: " + e.getMessage());
        }
    }
    
    @Override
    public CloudPodsServerSkuInstanceSpecsResponse getInstanceSpecs(
            CloudPodsServerSkuInstanceSpecsQueryRequest queryRequest) {
        try {
            log.info("获取实例规格配置: {}", queryRequest);
            
            Session adminSession = getAdminSession();
            SkuManager serverSkuManager = new SkuManager();
            
            // 构建查询参数
            JSONObject queryParams = buildInstanceSpecsQueryParams(queryRequest);
            log.info("CloudPods实例规格查询参数: {}", queryParams.toJSONString());
            
            // 调用instance-specs接口
            JSONObject result = serverSkuManager.PerformClassAction(adminSession, "instance-specs", queryParams);
            log.info("CloudPods实例规格响应: {}", result.toJSONString());
            
            return parseInstanceSpecsResponse(result);
            
        } catch (Exception e) {
            log.error("获取实例规格配置失败", e);
            return new CloudPodsServerSkuInstanceSpecsResponse()
                    .setSuccess(false)
                    .setErrorMessage("获取实例规格配置失败: " + e.getMessage());
        }
    }
    
    @Override
    public CloudPodsServerSkuDistinctFieldResponse getDistinctField(
            CloudPodsServerSkuDistinctFieldQueryRequest queryRequest) {
        try {
            log.info("获取字段去重值: {}", queryRequest);
            
            Session adminSession = getAdminSession();
            SkuManager serverSkuManager = new SkuManager();
            
            // 构建查询参数
            JSONObject queryParams = buildDistinctFieldQueryParams(queryRequest);
            log.info("CloudPods字段去重查询参数: {}", queryParams.toJSONString());
            
            // 调用distinct-field接口
            JSONObject result = serverSkuManager.PerformClassAction(adminSession, "distinct-field", queryParams);
            log.info("CloudPods字段去重响应: {}", result.toJSONString());
            
            return parseDistinctFieldResponse(result, queryRequest.getField());
            
        } catch (Exception e) {
            log.error("获取字段去重值失败", e);
            return new CloudPodsServerSkuDistinctFieldResponse()
                    .setSuccess(false)
                    .setErrorMessage("获取字段去重值失败: " + e.getMessage());
        }
    }
    
    @Override
    public CloudPodsServerSkuStatisticsResponse getStatistics(CloudPodsServerSkuStatisticsQueryRequest queryRequest) {
        try {
            log.info("获取ServerSku统计信息: {}", queryRequest);
            
            Session adminSession = getAdminSession();
            SkuManager serverSkuManager = new SkuManager();
            
            // 构建查询参数
            JSONObject queryParams = buildStatisticsQueryParams(queryRequest);
            log.info("CloudPods统计查询参数: {}", queryParams.toJSONString());
            
            // 调用statistics接口
            JSONObject result = serverSkuManager.PerformClassAction(adminSession, "statistics", queryParams);
            log.info("CloudPods统计响应: {}", result.toJSONString());
            
            return parseStatisticsResponse(result);
            
        } catch (Exception e) {
            log.error("获取ServerSku统计信息失败", e);
            return new CloudPodsServerSkuStatisticsResponse()
                    .setSuccess(false)
                    .setErrorMessage("获取ServerSku统计信息失败: " + e.getMessage());
        }
    }
    
    // ================== 私有方法 ==================
    
    /**
     * 构建ServerSku查询参数
     */
    private JSONObject buildServerSkuQueryParams(CloudPodsServerSkuQueryRequest request) {
        JSONObject params = new JSONObject();
        
        if (request.getLimit() != null) {
            params.put("limit", request.getLimit());
        }
        if (request.getOffset() != null) {
            params.put("offset", request.getOffset());
        }
        if (request.getOrderBy() != null) {
            params.put("order_by", request.getOrderBy());
        }
        if (request.getOrder() != null) {
            params.put("order", request.getOrder());
        }
        if (request.getDetails() != null) {
            params.put("details", request.getDetails());
        }
        if (request.getSearch() != null) {
            params.put("search", request.getSearch());
        }
        if (request.getFilter() != null) {
            params.put("filter", request.getFilter());
        }
        if (request.getEnabled() != null) {
            params.put("enabled", request.getEnabled());
        }
        if (request.getUsable() != null) {
            params.put("usable", request.getUsable());
        }
        if (request.getPublicCloud() != null) {
            params.put("public_cloud", request.getPublicCloud());
        }
        if (request.getCpuCoreCount() != null) {
            params.put("cpu_core_count", request.getCpuCoreCount());
        }
        if (request.getMemorySizeMb() != null) {
            params.put("memory_size_mb", request.getMemorySizeMb());
        }
        if (request.getCloudregion() != null) {
            params.put("cloudregion", request.getCloudregion());
        }
        if (request.getProvider() != null) {
            params.put("provider", request.getProvider());
        }
        if (request.getScope() != null) {
            params.put("scope", request.getScope());
        }
        if (request.getPostpaidStatus() != null) {
            params.put("postpaid_status", request.getPostpaidStatus());
        }
        if (request.getLocalCategory() != null) {
            params.put("local_category", request.getLocalCategory());
        }
        if (request.getDistinct() != null) {
            params.put("distinct", request.getDistinct());
        }
        if (request.getProjectId() != null) {
            params.put("project_id", request.getProjectId());
        }
        if (request.getDomainId() != null) {
            params.put("domain_id", request.getDomainId());
        }
        if (request.getZone() != null) {
            params.put("zone", request.getZone());
        }
        if (request.getHypervisor() != null) {
            params.put("hypervisor", request.getHypervisor());
        }
        if (request.getName() != null) {
            params.put("name", request.getName());
        }
        if (request.getId() != null) {
            params.put("id", request.getId());
        }
        if (request.getStatus() != null) {
            params.put("status", request.getStatus());
        }
        
        return params;
    }
    
    /**
     * 解析ServerSku列表响应
     */
    private CloudPodsServerSkuListResponse parseServerSkuListResponse(ListResult listResult) {
        CloudPodsServerSkuListResponse response = new CloudPodsServerSkuListResponse()
                .setSuccess(true)
                .setTotal(listResult.getTotal())
                .setLimit(20) // 默认分页大小
                .setOffset(0); // 默认偏移量
        
        List<CloudPodsServerSkuListResponse.ServerSkuSummary> serverSkus = new ArrayList<>();
        if (listResult.getData() != null) {
            for (Object obj : listResult.getData()) {
                JSONObject serverSkuObj = (JSONObject) obj;
                CloudPodsServerSkuListResponse.ServerSkuSummary serverSku = parseServerSkuSummary(serverSkuObj);
                serverSkus.add(serverSku);
            }
        }
        response.setServerSkus(serverSkus);
        
        return response;
    }
    
    /**
     * 解析ServerSku摘要信息
     */
    private CloudPodsServerSkuListResponse.ServerSkuSummary parseServerSkuSummary(JSONObject serverSkuObj) {
        CloudPodsServerSkuListResponse.ServerSkuSummary serverSku =
                new CloudPodsServerSkuListResponse.ServerSkuSummary();
        
        serverSku.setId(serverSkuObj.getString("id"))
                .setName(serverSkuObj.getString("name"))
                .setDescription(serverSkuObj.getString("description"))
                .setCpuCoreCount(serverSkuObj.getInteger("cpu_core_count"))
                .setMemorySizeMb(serverSkuObj.getInteger("memory_size_mb"))
                .setLocalCategory(serverSkuObj.getString("local_category"))
                .setInstanceTypeFamily(serverSkuObj.getString("instance_type_family"))
                .setInstanceType(serverSkuObj.getString("instance_type"))
                .setProvider(serverSkuObj.getString("provider"))
                .setCloudregion(serverSkuObj.getString("cloudregion"))
                .setCloudregionId(serverSkuObj.getString("cloudregion_id"))
                .setZone(serverSkuObj.getString("zone"))
                .setZoneId(serverSkuObj.getString("zone_id"))
                .setEnabled(serverSkuObj.getBoolean("enabled"))
                .setStatus(serverSkuObj.getString("status"))
                .setPublicCloud(serverSkuObj.getBoolean("public_cloud"))
                .setPostpaidStatus(serverSkuObj.getString("postpaid_status"))
                .setPrepaidStatus(serverSkuObj.getString("prepaid_status"))
                .setCreatedAt(serverSkuObj.getString("created_at"))
                .setUpdatedAt(serverSkuObj.getString("updated_at"))
                .setProjectId(serverSkuObj.getString("project_id"))
                .setProject(serverSkuObj.getString("project"))
                .setDomainId(serverSkuObj.getString("domain_id"))
                .setDomain(serverSkuObj.getString("domain"))
                .setHypervisor(serverSkuObj.getString("hypervisor"))
                .setOsArch(serverSkuObj.getString("os_arch"))
                .setGpuCount(serverSkuObj.getInteger("gpu_count"))
                .setGpuSpec(serverSkuObj.getString("gpu_spec"))
                .setNetworkMaxBandwidth(serverSkuObj.getInteger("network_max_bandwidth"))
                .setNetworkMaxConnections(serverSkuObj.getInteger("network_max_connections"))
                .setStorageType(serverSkuObj.getString("storage_type"))
                .setDataDiskTypes(serverSkuObj.getString("data_disk_types"))
                .setDataDiskMaxCount(serverSkuObj.getInteger("data_disk_max_count"))
                .setDataDiskMaxSizeGb(serverSkuObj.getInteger("data_disk_max_size_gb"))
                .setSysDiskMinSizeGb(serverSkuObj.getInteger("sys_disk_min_size_gb"))
                .setSysDiskMaxSizeGb(serverSkuObj.getInteger("sys_disk_max_size_gb"))
                .setSysDiskType(serverSkuObj.getString("sys_disk_type"))
                .setHotplugCpuMem(serverSkuObj.getBoolean("hotplug_cpu_mem"))
                .setTags(serverSkuObj.getString("tags"))
                .setMetadata(serverSkuObj.getString("metadata"));
        
        return serverSku;
    }
    
    /**
     * 解析ServerSku详情响应
     */
    private CloudPodsServerSkuDetailResponse parseServerSkuDetailResponse(JSONObject result) {
        CloudPodsServerSkuDetailResponse response = new CloudPodsServerSkuDetailResponse().setSuccess(true);
        
        CloudPodsServerSkuDetailResponse.ServerSkuDetail serverSkuDetail =
                new CloudPodsServerSkuDetailResponse.ServerSkuDetail();
        
        serverSkuDetail.setId(result.getString("id"))
                .setName(result.getString("name"))
                .setDescription(result.getString("description"))
                .setCpuCoreCount(result.getInteger("cpu_core_count"))
                .setMemorySizeMb(result.getInteger("memory_size_mb"))
                .setLocalCategory(result.getString("local_category"))
                .setInstanceTypeFamily(result.getString("instance_type_family"))
                .setInstanceType(result.getString("instance_type"))
                .setProvider(result.getString("provider"))
                .setCloudregion(result.getString("cloudregion"))
                .setCloudregionId(result.getString("cloudregion_id"))
                .setZone(result.getString("zone"))
                .setZoneId(result.getString("zone_id"))
                .setEnabled(result.getBoolean("enabled"))
                .setStatus(result.getString("status"))
                .setPublicCloud(result.getBoolean("public_cloud"))
                .setPostpaidStatus(result.getString("postpaid_status"))
                .setPrepaidStatus(result.getString("prepaid_status"))
                .setCreatedAt(result.getString("created_at"))
                .setUpdatedAt(result.getString("updated_at"))
                .setProjectId(result.getString("project_id"))
                .setProject(result.getString("project"))
                .setDomainId(result.getString("domain_id"))
                .setDomain(result.getString("domain"))
                .setHypervisor(result.getString("hypervisor"))
                .setOsArch(result.getString("os_arch"))
                .setGpuCount(result.getInteger("gpu_count"))
                .setGpuSpec(result.getString("gpu_spec"))
                .setNetworkMaxBandwidth(result.getInteger("network_max_bandwidth"))
                .setNetworkMaxConnections(result.getInteger("network_max_connections"))
                .setStorageType(result.getString("storage_type"))
                .setDataDiskTypes(result.getString("data_disk_types"))
                .setDataDiskMaxCount(result.getInteger("data_disk_max_count"))
                .setDataDiskMaxSizeGb(result.getInteger("data_disk_max_size_gb"))
                .setSysDiskMinSizeGb(result.getInteger("sys_disk_min_size_gb"))
                .setSysDiskMaxSizeGb(result.getInteger("sys_disk_max_size_gb"))
                .setSysDiskType(result.getString("sys_disk_type"))
                .setHotplugCpuMem(result.getBoolean("hotplug_cpu_mem"));
        
        // 解析标签和元数据
        if (result.containsKey("tags") && result.get("tags") instanceof JSONObject) {
            Map<String, String> tags = new HashMap<>();
            JSONObject tagsObj = result.getJSONObject("tags");
            for (String key : tagsObj.keySet()) {
                tags.put(key, tagsObj.getString(key));
            }
            serverSkuDetail.setTags(tags);
        }
        
        if (result.containsKey("metadata") && result.get("metadata") instanceof JSONObject) {
            Map<String, Object> metadata = new HashMap<>();
            JSONObject metadataObj = result.getJSONObject("metadata");
            for (String key : metadataObj.keySet()) {
                metadata.put(key, metadataObj.get(key));
            }
            serverSkuDetail.setMetadata(metadata);
        }
        
        response.setServerSku(serverSkuDetail);
        return response;
    }
    
    /**
     * 构建创建ServerSku参数
     */
    private JSONObject buildCreateServerSkuParams(CloudPodsServerSkuCreateRequest request) {
        JSONObject params = new JSONObject();
        
        if (request.getName() != null) {
            params.put("name", request.getName());
        }
        if (request.getDescription() != null) {
            params.put("description", request.getDescription());
        }
        if (request.getCpuCoreCount() != null) {
            params.put("cpu_core_count", request.getCpuCoreCount());
        }
        if (request.getMemorySizeMb() != null) {
            params.put("memory_size_mb", request.getMemorySizeMb());
        }
        if (request.getLocalCategory() != null) {
            params.put("local_category", request.getLocalCategory());
        }
        if (request.getInstanceTypeFamily() != null) {
            params.put("instance_type_family", request.getInstanceTypeFamily());
        }
        if (request.getInstanceType() != null) {
            params.put("instance_type", request.getInstanceType());
        }
        if (request.getProvider() != null) {
            params.put("provider", request.getProvider());
        }
        if (request.getCloudregionId() != null) {
            params.put("cloudregion_id", request.getCloudregionId());
        }
        if (request.getZoneId() != null) {
            params.put("zone_id", request.getZoneId());
        }
        if (request.getEnabled() != null) {
            params.put("enabled", request.getEnabled());
        }
        if (request.getPublicCloud() != null) {
            params.put("public_cloud", request.getPublicCloud());
        }
        if (request.getPostpaidStatus() != null) {
            params.put("postpaid_status", request.getPostpaidStatus());
        }
        if (request.getPrepaidStatus() != null) {
            params.put("prepaid_status", request.getPrepaidStatus());
        }
        if (request.getProjectId() != null) {
            params.put("project_id", request.getProjectId());
        }
        if (request.getDomainId() != null) {
            params.put("domain_id", request.getDomainId());
        }
        if (request.getHypervisor() != null) {
            params.put("hypervisor", request.getHypervisor());
        }
        if (request.getOsArch() != null) {
            params.put("os_arch", request.getOsArch());
        }
        if (request.getGpuCount() != null) {
            params.put("gpu_count", request.getGpuCount());
        }
        if (request.getGpuSpec() != null) {
            params.put("gpu_spec", request.getGpuSpec());
        }
        if (request.getNetworkMaxBandwidth() != null) {
            params.put("network_max_bandwidth", request.getNetworkMaxBandwidth());
        }
        if (request.getNetworkMaxConnections() != null) {
            params.put("network_max_connections", request.getNetworkMaxConnections());
        }
        if (request.getStorageType() != null) {
            params.put("storage_type", request.getStorageType());
        }
        if (request.getDataDiskTypes() != null) {
            params.put("data_disk_types", request.getDataDiskTypes());
        }
        if (request.getDataDiskMaxCount() != null) {
            params.put("data_disk_max_count", request.getDataDiskMaxCount());
        }
        if (request.getDataDiskMaxSizeGb() != null) {
            params.put("data_disk_max_size_gb", request.getDataDiskMaxSizeGb());
        }
        if (request.getSysDiskMinSizeGb() != null) {
            params.put("sys_disk_min_size_gb", request.getSysDiskMinSizeGb());
        }
        if (request.getSysDiskMaxSizeGb() != null) {
            params.put("sys_disk_max_size_gb", request.getSysDiskMaxSizeGb());
        }
        if (request.getSysDiskType() != null) {
            params.put("sys_disk_type", request.getSysDiskType());
        }
        if (request.getHotplugCpuMem() != null) {
            params.put("hotplug_cpu_mem", request.getHotplugCpuMem());
        }
        
        // 添加标签和元数据
        if (request.getTags() != null && !request.getTags().isEmpty()) {
            JSONObject tagsObj = new JSONObject();
            for (Map.Entry<String, String> entry : request.getTags().entrySet()) {
                tagsObj.put(entry.getKey(), entry.getValue());
            }
            params.put("tags", tagsObj);
        }
        
        if (request.getMetadata() != null && !request.getMetadata().isEmpty()) {
            JSONObject metadataObj = new JSONObject();
            for (Map.Entry<String, Object> entry : request.getMetadata().entrySet()) {
                metadataObj.put(entry.getKey(), entry.getValue());
            }
            params.put("metadata", metadataObj);
        }
        
        return params;
    }
    
    /**
     * 解析创建ServerSku响应
     */
    private CloudPodsServerSkuCreateResponse parseCreateServerSkuResponse(JSONObject result) {
        String serverSkuId = result.getString("id");
        String serverSkuName = result.getString("name");
        return CloudPodsServerSkuCreateResponse.success(serverSkuId, serverSkuName, result.toJSONString());
    }
    
    /**
     * 构建更新ServerSku参数
     */
    private JSONObject buildUpdateServerSkuParams(CloudPodsServerSkuUpdateRequest request) {
        JSONObject params = new JSONObject();
        
        if (request.getName() != null) {
            params.put("name", request.getName());
        }
        if (request.getDescription() != null) {
            params.put("description", request.getDescription());
        }
        if (request.getCpuCoreCount() != null) {
            params.put("cpu_core_count", request.getCpuCoreCount());
        }
        if (request.getMemorySizeMb() != null) {
            params.put("memory_size_mb", request.getMemorySizeMb());
        }
        if (request.getLocalCategory() != null) {
            params.put("local_category", request.getLocalCategory());
        }
        if (request.getInstanceTypeFamily() != null) {
            params.put("instance_type_family", request.getInstanceTypeFamily());
        }
        if (request.getInstanceType() != null) {
            params.put("instance_type", request.getInstanceType());
        }
        if (request.getEnabled() != null) {
            params.put("enabled", request.getEnabled());
        }
        if (request.getPostpaidStatus() != null) {
            params.put("postpaid_status", request.getPostpaidStatus());
        }
        if (request.getPrepaidStatus() != null) {
            params.put("prepaid_status", request.getPrepaidStatus());
        }
        if (request.getGpuCount() != null) {
            params.put("gpu_count", request.getGpuCount());
        }
        if (request.getGpuSpec() != null) {
            params.put("gpu_spec", request.getGpuSpec());
        }
        if (request.getNetworkMaxBandwidth() != null) {
            params.put("network_max_bandwidth", request.getNetworkMaxBandwidth());
        }
        if (request.getNetworkMaxConnections() != null) {
            params.put("network_max_connections", request.getNetworkMaxConnections());
        }
        if (request.getStorageType() != null) {
            params.put("storage_type", request.getStorageType());
        }
        if (request.getDataDiskTypes() != null) {
            params.put("data_disk_types", request.getDataDiskTypes());
        }
        if (request.getDataDiskMaxCount() != null) {
            params.put("data_disk_max_count", request.getDataDiskMaxCount());
        }
        if (request.getDataDiskMaxSizeGb() != null) {
            params.put("data_disk_max_size_gb", request.getDataDiskMaxSizeGb());
        }
        if (request.getSysDiskMinSizeGb() != null) {
            params.put("sys_disk_min_size_gb", request.getSysDiskMinSizeGb());
        }
        if (request.getSysDiskMaxSizeGb() != null) {
            params.put("sys_disk_max_size_gb", request.getSysDiskMaxSizeGb());
        }
        if (request.getSysDiskType() != null) {
            params.put("sys_disk_type", request.getSysDiskType());
        }
        if (request.getHotplugCpuMem() != null) {
            params.put("hotplug_cpu_mem", request.getHotplugCpuMem());
        }
        
        // 添加标签和元数据
        if (request.getTags() != null && !request.getTags().isEmpty()) {
            JSONObject tagsObj = new JSONObject();
            for (Map.Entry<String, String> entry : request.getTags().entrySet()) {
                tagsObj.put(entry.getKey(), entry.getValue());
            }
            params.put("tags", tagsObj);
        }
        
        if (request.getMetadata() != null && !request.getMetadata().isEmpty()) {
            JSONObject metadataObj = new JSONObject();
            for (Map.Entry<String, Object> entry : request.getMetadata().entrySet()) {
                metadataObj.put(entry.getKey(), entry.getValue());
            }
            params.put("metadata", metadataObj);
        }
        
        return params;
    }
    
    /**
     * 解析更新ServerSku响应
     */
    private CloudPodsServerSkuUpdateResponse parseUpdateServerSkuResponse(JSONObject result) {
        String serverSkuId = result.getString("id");
        String serverSkuName = result.getString("name");
        return CloudPodsServerSkuUpdateResponse.success(serverSkuId, serverSkuName, result.toJSONString());
    }
    
    /**
     * 解析删除ServerSku响应
     */
    private CloudPodsServerSkuDeleteResponse parseDeleteServerSkuResponse(JSONObject result, String skuId) {
        String serverSkuName = result.getString("name");
        return CloudPodsServerSkuDeleteResponse.success(skuId, serverSkuName, result.toJSONString());
    }
    
    /**
     * 构建实例规格查询参数
     */
    private JSONObject buildInstanceSpecsQueryParams(CloudPodsServerSkuInstanceSpecsQueryRequest request) {
        JSONObject params = new JSONObject();
        
        if (request.getUsable() != null) {
            params.put("usable", request.getUsable());
        }
        if (request.getEnabled() != null) {
            params.put("enabled", request.getEnabled());
        }
        if (request.getCloudregion() != null) {
            params.put("cloudregion", request.getCloudregion());
        }
        if (request.getProvider() != null) {
            params.put("provider", request.getProvider());
        }
        if (request.getPublicCloud() != null) {
            params.put("public_cloud", request.getPublicCloud());
        }
        if (request.getPostpaidStatus() != null) {
            params.put("postpaid_status", request.getPostpaidStatus());
        }
        if (request.getPrepaidStatus() != null) {
            params.put("prepaid_status", request.getPrepaidStatus());
        }
        if (request.getCpuCoreCount() != null) {
            params.put("cpu_core_count", request.getCpuCoreCount());
        }
        if (request.getMemorySizeMb() != null) {
            params.put("memory_size_mb", request.getMemorySizeMb());
        }
        if (request.getLocalCategory() != null) {
            params.put("local_category", request.getLocalCategory());
        }
        if (request.getHypervisor() != null) {
            params.put("hypervisor", request.getHypervisor());
        }
        if (request.getOsArch() != null) {
            params.put("os_arch", request.getOsArch());
        }
        if (request.getZone() != null) {
            params.put("zone", request.getZone());
        }
        if (request.getProjectId() != null) {
            params.put("project_id", request.getProjectId());
        }
        if (request.getDomainId() != null) {
            params.put("domain_id", request.getDomainId());
        }
        if (request.getScope() != null) {
            params.put("scope", request.getScope());
        }
        if (request.getStorageType() != null) {
            params.put("storage_type", request.getStorageType());
        }
        if (request.getGpuCount() != null) {
            params.put("gpu_count", request.getGpuCount());
        }
        if (request.getNetworkType() != null) {
            params.put("network_type", request.getNetworkType());
        }
        if (request.getHotplugCpuMem() != null) {
            params.put("hotplug_cpu_mem", request.getHotplugCpuMem());
        }
        if (request.getInstanceTypeFamily() != null) {
            params.put("instance_type_family", request.getInstanceTypeFamily());
        }
        if (request.getInstanceType() != null) {
            params.put("instance_type", request.getInstanceType());
        }
        
        return params;
    }
    
    /**
     * 解析实例规格响应
     */
    private CloudPodsServerSkuInstanceSpecsResponse parseInstanceSpecsResponse(JSONObject result) {
        CloudPodsServerSkuInstanceSpecsResponse response = new CloudPodsServerSkuInstanceSpecsResponse()
                .setSuccess(true);
        
        // 解析CPU规格
        if (result.containsKey("cpu_specs") && result.get("cpu_specs") instanceof JSONArray) {
            List<Integer> cpuSpecs = new ArrayList<>();
            JSONArray cpuArray = result.getJSONArray("cpu_specs");
            for (int i = 0; i < cpuArray.size(); i++) {
                cpuSpecs.add(cpuArray.getInteger(i));
            }
            response.setCpuSpecs(cpuSpecs);
        }
        
        // 解析内存规格
        if (result.containsKey("memory_specs") && result.get("memory_specs") instanceof JSONArray) {
            List<Integer> memorySpecs = new ArrayList<>();
            JSONArray memoryArray = result.getJSONArray("memory_specs");
            for (int i = 0; i < memoryArray.size(); i++) {
                memorySpecs.add(memoryArray.getInteger(i));
            }
            response.setMemorySpecs(memorySpecs);
        }
        
        // 解析实例规格组合
        if (result.containsKey("instance_specs") && result.get("instance_specs") instanceof JSONArray) {
            List<CloudPodsServerSkuInstanceSpecsResponse.InstanceSpec> instanceSpecs = new ArrayList<>();
            JSONArray instanceArray = result.getJSONArray("instance_specs");
            for (int i = 0; i < instanceArray.size(); i++) {
                JSONObject instanceObj = instanceArray.getJSONObject(i);
                CloudPodsServerSkuInstanceSpecsResponse.InstanceSpec instanceSpec =
                        new CloudPodsServerSkuInstanceSpecsResponse.InstanceSpec()
                                .setCpuCoreCount(instanceObj.getInteger("cpu_core_count"))
                                .setMemorySizeMb(instanceObj.getInteger("memory_size_mb"))
                                .setLocalCategory(instanceObj.getString("local_category"))
                                .setInstanceType(instanceObj.getString("instance_type"))
                                .setAvailable(instanceObj.getBoolean("available"));
                instanceSpecs.add(instanceSpec);
            }
            response.setInstanceSpecs(instanceSpecs);
        }
        
        // 解析存储类型信息
        if (result.containsKey("storage_types") && result.get("storage_types") instanceof JSONObject) {
            Map<String, CloudPodsServerSkuInstanceSpecsResponse.StorageTypeInfo> storageTypes = new HashMap<>();
            JSONObject storageObj = result.getJSONObject("storage_types");
            for (String key : storageObj.keySet()) {
                JSONObject typeObj = storageObj.getJSONObject(key);
                CloudPodsServerSkuInstanceSpecsResponse.StorageTypeInfo storageTypeInfo =
                        new CloudPodsServerSkuInstanceSpecsResponse.StorageTypeInfo()
                                .setName(key)
                                .setDescription(typeObj.getString("description"))
                                .setTotalCapacity(typeObj.getLong("total_capacity"))
                                .setAvailableCapacity(typeObj.getLong("available_capacity"))
                                .setUsedCapacity(typeObj.getLong("used_capacity"))
                                .setIsSysDiskStore(typeObj.getBoolean("is_sys_disk_store"));
                storageTypes.put(key, storageTypeInfo);
            }
            response.setStorageTypes(storageTypes);
        }
        
        return response;
    }
    
    /**
     * 构建字段去重查询参数
     */
    private JSONObject buildDistinctFieldQueryParams(CloudPodsServerSkuDistinctFieldQueryRequest request) {
        JSONObject params = new JSONObject();
        
        if (request.getField() != null) {
            params.put("field", request.getField());
        }
        if (request.getPublicCloud() != null) {
            params.put("public_cloud", request.getPublicCloud());
        }
        if (request.getPostpaidStatus() != null) {
            params.put("postpaid_status", request.getPostpaidStatus());
        }
        if (request.getPrepaidStatus() != null) {
            params.put("prepaid_status", request.getPrepaidStatus());
        }
        if (request.getCpuCoreCount() != null) {
            params.put("cpu_core_count", request.getCpuCoreCount());
        }
        if (request.getMemorySizeMb() != null) {
            params.put("memory_size_mb", request.getMemorySizeMb());
        }
        if (request.getCloudregion() != null) {
            params.put("cloudregion", request.getCloudregion());
        }
        if (request.getProvider() != null) {
            params.put("provider", request.getProvider());
        }
        if (request.getScope() != null) {
            params.put("scope", request.getScope());
        }
        if (request.getLocalCategory() != null) {
            params.put("local_category", request.getLocalCategory());
        }
        if (request.getHypervisor() != null) {
            params.put("hypervisor", request.getHypervisor());
        }
        if (request.getOsArch() != null) {
            params.put("os_arch", request.getOsArch());
        }
        if (request.getZone() != null) {
            params.put("zone", request.getZone());
        }
        if (request.getProjectId() != null) {
            params.put("project_id", request.getProjectId());
        }
        if (request.getDomainId() != null) {
            params.put("domain_id", request.getDomainId());
        }
        if (request.getEnabled() != null) {
            params.put("enabled", request.getEnabled());
        }
        if (request.getUsable() != null) {
            params.put("usable", request.getUsable());
        }
        if (request.getStorageType() != null) {
            params.put("storage_type", request.getStorageType());
        }
        if (request.getGpuCount() != null) {
            params.put("gpu_count", request.getGpuCount());
        }
        if (request.getNetworkType() != null) {
            params.put("network_type", request.getNetworkType());
        }
        if (request.getInstanceTypeFamily() != null) {
            params.put("instance_type_family", request.getInstanceTypeFamily());
        }
        if (request.getInstanceType() != null) {
            params.put("instance_type", request.getInstanceType());
        }
        if (request.getStatus() != null) {
            params.put("status", request.getStatus());
        }
        if (request.getSearch() != null) {
            params.put("search", request.getSearch());
        }
        if (request.getFilter() != null) {
            params.put("filter", request.getFilter());
        }
        
        return params;
    }
    
    /**
     * 解析字段去重响应
     */
    private CloudPodsServerSkuDistinctFieldResponse parseDistinctFieldResponse(JSONObject result, String field) {
        CloudPodsServerSkuDistinctFieldResponse response = new CloudPodsServerSkuDistinctFieldResponse()
                .setSuccess(true)
                .setField(field);
        
        if (result.containsKey("data") && result.get("data") instanceof JSONArray) {
            List<String> values = new ArrayList<>();
            List<CloudPodsServerSkuDistinctFieldResponse.FieldValueInfo> valueInfos = new ArrayList<>();
            
            JSONArray dataArray = result.getJSONArray("data");
            for (int i = 0; i < dataArray.size(); i++) {
                if (dataArray.get(i) instanceof String) {
                    String value = dataArray.getString(i);
                    values.add(value);
                    
                    CloudPodsServerSkuDistinctFieldResponse.FieldValueInfo valueInfo =
                            new CloudPodsServerSkuDistinctFieldResponse.FieldValueInfo()
                                    .setValue(value)
                                    .setDisplayName(value)
                                    .setAvailable(true);
                    valueInfos.add(valueInfo);
                }
            }
            
            response.setValues(values);
            response.setValueInfos(valueInfos);
            response.setTotal(values.size());
        }
        
        return response;
    }
    
    /**
     * 构建统计查询参数
     */
    private JSONObject buildStatisticsQueryParams(CloudPodsServerSkuStatisticsQueryRequest request) {
        JSONObject params = new JSONObject();
        
        if (request.getCloudregion() != null) {
            params.put("cloudregion", request.getCloudregion());
        }
        if (request.getProvider() != null) {
            params.put("provider", request.getProvider());
        }
        if (request.getPublicCloud() != null) {
            params.put("public_cloud", request.getPublicCloud());
        }
        if (request.getHypervisor() != null) {
            params.put("hypervisor", request.getHypervisor());
        }
        if (request.getOsArch() != null) {
            params.put("os_arch", request.getOsArch());
        }
        if (request.getZone() != null) {
            params.put("zone", request.getZone());
        }
        if (request.getProjectId() != null) {
            params.put("project_id", request.getProjectId());
        }
        if (request.getDomainId() != null) {
            params.put("domain_id", request.getDomainId());
        }
        if (request.getScope() != null) {
            params.put("scope", request.getScope());
        }
        if (request.getLocalCategory() != null) {
            params.put("local_category", request.getLocalCategory());
        }
        if (request.getInstanceTypeFamily() != null) {
            params.put("instance_type_family", request.getInstanceTypeFamily());
        }
        if (request.getEnabled() != null) {
            params.put("enabled", request.getEnabled());
        }
        if (request.getUsable() != null) {
            params.put("usable", request.getUsable());
        }
        if (request.getPostpaidStatus() != null) {
            params.put("postpaid_status", request.getPostpaidStatus());
        }
        if (request.getPrepaidStatus() != null) {
            params.put("prepaid_status", request.getPrepaidStatus());
        }
        if (request.getStatus() != null) {
            params.put("status", request.getStatus());
        }
        if (request.getStatisticsType() != null) {
            params.put("statistics_type", request.getStatisticsType());
        }
        if (request.getGroupBy() != null) {
            params.put("group_by", request.getGroupBy());
        }
        if (request.getTimeRangeStart() != null) {
            params.put("time_range_start", request.getTimeRangeStart());
        }
        if (request.getTimeRangeEnd() != null) {
            params.put("time_range_end", request.getTimeRangeEnd());
        }
        if (request.getSearch() != null) {
            params.put("search", request.getSearch());
        }
        if (request.getFilter() != null) {
            params.put("filter", request.getFilter());
        }
        
        return params;
    }
    
    /**
     * 解析统计响应
     */
    private CloudPodsServerSkuStatisticsResponse parseStatisticsResponse(JSONObject result) {
        CloudPodsServerSkuStatisticsResponse response = new CloudPodsServerSkuStatisticsResponse()
                .setSuccess(true);
        
        // 解析总体统计信息
        if (result.containsKey("overall") && result.get("overall") instanceof JSONObject) {
            JSONObject overallObj = result.getJSONObject("overall");
            CloudPodsServerSkuStatisticsResponse.OverallStatistics overallStats =
                    new CloudPodsServerSkuStatisticsResponse.OverallStatistics()
                            .setTotalCount(overallObj.getInteger("total_count"))
                            .setEnabledCount(overallObj.getInteger("enabled_count"))
                            .setDisabledCount(overallObj.getInteger("disabled_count"))
                            .setAvailableCount(overallObj.getInteger("available_count"))
                            .setUnavailableCount(overallObj.getInteger("unavailable_count"))
                            .setPublicCloudCount(overallObj.getInteger("public_cloud_count"))
                            .setPrivateCloudCount(overallObj.getInteger("private_cloud_count"))
                            .setTotalCpuCores(overallObj.getLong("total_cpu_cores"))
                            .setTotalMemoryMb(overallObj.getLong("total_memory_mb"))
                            .setAverageCpuCores(overallObj.getDouble("average_cpu_cores"))
                            .setAverageMemoryMb(overallObj.getDouble("average_memory_mb"))
                            .setMinCpuCores(overallObj.getInteger("min_cpu_cores"))
                            .setMaxCpuCores(overallObj.getInteger("max_cpu_cores"))
                            .setMinMemoryMb(overallObj.getInteger("min_memory_mb"))
                            .setMaxMemoryMb(overallObj.getInteger("max_memory_mb"));
            response.setOverallStatistics(overallStats);
        }
        
        // 解析分组统计信息
        if (result.containsKey("groups") && result.get("groups") instanceof JSONArray) {
            List<CloudPodsServerSkuStatisticsResponse.GroupStatistics> groupStatistics = new ArrayList<>();
            JSONArray groupsArray = result.getJSONArray("groups");
            for (int i = 0; i < groupsArray.size(); i++) {
                JSONObject groupObj = groupsArray.getJSONObject(i);
                CloudPodsServerSkuStatisticsResponse.GroupStatistics groupStats =
                        new CloudPodsServerSkuStatisticsResponse.GroupStatistics()
                                .setGroupField(groupObj.getString("group_field"))
                                .setGroupValue(groupObj.getString("group_value"))
                                .setGroupDisplayName(groupObj.getString("group_display_name"))
                                .setCount(groupObj.getInteger("count"))
                                .setPercentage(groupObj.getDouble("percentage"));
                groupStatistics.add(groupStats);
            }
            response.setGroupStatistics(groupStatistics);
        }
        
        return response;
    }
}
